"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/voyages.tsx":
/*!*************************************!*\
  !*** ./src/app/ui/crew/voyages.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.24_@ba_5aaac57c1bd8be9e9c8518a8e7af1c46/node_modules/nuqs/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst CrewVoyages = (param)=>{\n    let { voyages } = param;\n    _s();\n    // State management for filters using nuqs\n    const [dateRangeFilter, setDateRangeFilter] = (0,nuqs__WEBPACK_IMPORTED_MODULE_6__.useQueryState)(\"dateRange\", {\n        defaultValue: \"\",\n        serialize: (value)=>value || \"\",\n        parse: (value)=>value || \"\"\n    });\n    const [vesselFilter, setVesselFilter] = (0,nuqs__WEBPACK_IMPORTED_MODULE_6__.useQueryState)(\"vessel\", {\n        defaultValue: \"\",\n        serialize: (value)=>value || \"\",\n        parse: (value)=>value || \"\"\n    });\n    const [dutyFilter, setDutyFilter] = (0,nuqs__WEBPACK_IMPORTED_MODULE_6__.useQueryState)(\"duty\", {\n        defaultValue: \"\",\n        serialize: (value)=>value || \"\",\n        parse: (value)=>value || \"\"\n    });\n    // Local state for filter values\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [selectedVessel, setSelectedVessel] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [selectedDuty, setSelectedDuty] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const formatDateWithTime = (dateTime)=>{\n        if (dateTime) {\n            const [date, time] = dateTime.split(\" \");\n            const [year, month, day] = date.split(\"-\");\n            return \"\".concat(day, \"/\").concat(month, \"/\").concat(year.slice(-2), \" at \").concat(time);\n        }\n    };\n    // Extract unique vessel options from voyages data\n    const vesselOptions = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(()=>{\n        if (!voyages || !Array.isArray(voyages)) return [];\n        const uniqueVessels = new Map();\n        voyages.forEach((voyage)=>{\n            var _voyage_logBookEntry;\n            const vessel = voyage === null || voyage === void 0 ? void 0 : (_voyage_logBookEntry = voyage.logBookEntry) === null || _voyage_logBookEntry === void 0 ? void 0 : _voyage_logBookEntry.vehicle;\n            if (vessel && vessel.id && vessel.title) {\n                uniqueVessels.set(vessel.id, {\n                    value: vessel.id,\n                    label: vessel.title\n                });\n            }\n        });\n        return Array.from(uniqueVessels.values()).sort((a, b)=>a.label.localeCompare(b.label));\n    }, [\n        voyages\n    ]);\n    // Extract unique duty options from voyages data\n    const dutyOptions = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(()=>{\n        if (!voyages || !Array.isArray(voyages)) return [];\n        const uniqueDuties = new Map();\n        voyages.forEach((voyage)=>{\n            const duty = voyage === null || voyage === void 0 ? void 0 : voyage.dutyPerformed;\n            if (duty && duty.id && duty.title) {\n                uniqueDuties.set(duty.id, {\n                    value: duty.id,\n                    label: duty.title\n                });\n            }\n        });\n        return Array.from(uniqueDuties.values()).sort((a, b)=>a.label.localeCompare(b.label));\n    }, [\n        voyages\n    ]);\n    // Filter voyages based on active filters\n    const filteredVoyages = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(()=>{\n        if (!voyages || !Array.isArray(voyages)) return [];\n        let filtered = [\n            ...voyages\n        ];\n        // Apply date range filter\n        if (dateRange && (dateRange.from || dateRange.to)) {\n            filtered = filtered.filter((voyage)=>{\n                const voyageDate = voyage.punchIn ? dayjs__WEBPACK_IMPORTED_MODULE_2___default()(voyage.punchIn) : dayjs__WEBPACK_IMPORTED_MODULE_2___default()(voyage.logBookEntry.startDate);\n                if (dateRange.from && dateRange.to) {\n                    return voyageDate.isBetween(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(dateRange.from).startOf(\"day\"), dayjs__WEBPACK_IMPORTED_MODULE_2___default()(dateRange.to).endOf(\"day\"), null, \"[]\");\n                } else if (dateRange.from) {\n                    return voyageDate.isAfter(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(dateRange.from).startOf(\"day\")) || voyageDate.isSame(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(dateRange.from), \"day\");\n                } else if (dateRange.to) {\n                    return voyageDate.isBefore(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(dateRange.to).endOf(\"day\")) || voyageDate.isSame(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(dateRange.to), \"day\");\n                }\n                return true;\n            });\n        }\n        // Apply vessel filter\n        if (selectedVessel) {\n            const vesselId = String(selectedVessel.value);\n            filtered = filtered.filter((voyage)=>{\n                var _voyage_logBookEntry_vehicle, _voyage_logBookEntry;\n                const voyageVesselId = String(voyage === null || voyage === void 0 ? void 0 : (_voyage_logBookEntry = voyage.logBookEntry) === null || _voyage_logBookEntry === void 0 ? void 0 : (_voyage_logBookEntry_vehicle = _voyage_logBookEntry.vehicle) === null || _voyage_logBookEntry_vehicle === void 0 ? void 0 : _voyage_logBookEntry_vehicle.id);\n                return voyageVesselId === vesselId;\n            });\n        }\n        // Apply duty performed filter\n        if (selectedDuty) {\n            const dutyId = String(selectedDuty.value);\n            filtered = filtered.filter((voyage)=>{\n                var _voyage_dutyPerformed;\n                const voyageDutyId = String(voyage === null || voyage === void 0 ? void 0 : (_voyage_dutyPerformed = voyage.dutyPerformed) === null || _voyage_dutyPerformed === void 0 ? void 0 : _voyage_dutyPerformed.id);\n                return voyageDutyId === dutyId;\n            });\n        }\n        return filtered;\n    }, [\n        voyages,\n        dateRange,\n        selectedVessel,\n        selectedDuty\n    ]);\n    // Calculate sea time in hours\n    const calculateSeaTime = (punchIn, punchOut)=>{\n        if (!punchIn || !punchOut) return \"0\";\n        const hours = Math.floor((dayjs__WEBPACK_IMPORTED_MODULE_2___default()(punchOut).valueOf() - dayjs__WEBPACK_IMPORTED_MODULE_2___default()(punchIn).valueOf()) / (1000 * 60 * 60));\n        return isNaN(hours) ? \"0\" : hours.toString();\n    };\n    // Define columns for the DataTable\n    const columns = [\n        {\n            accessorKey: \"date\",\n            header: \"Date\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.punchIn ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_3__.formatDate)(voyage.punchIn) : (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_3__.formatDate)(voyage.logBookEntry.startDate);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: \"Vessel\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.logBookEntry.vehicle.title;\n            }\n        },\n        {\n            accessorKey: \"dutyPerformed\",\n            header: \"Duty performed\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.dutyPerformed.title;\n            }\n        },\n        {\n            accessorKey: \"signIn\",\n            header: \"Sign in\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return formatDateWithTime(voyage.punchIn);\n            }\n        },\n        {\n            accessorKey: \"signOut\",\n            header: \"Sign out\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return formatDateWithTime(voyage.punchOut);\n            }\n        },\n        {\n            accessorKey: \"totalSeaTime\",\n            header: \"Total sea time\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                const hours = calculateSeaTime(voyage.punchIn, voyage.punchOut);\n                return \"\".concat(hours, \" Hours\");\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full p-0\",\n        children: !voyages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_1__.List, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n            lineNumber: 217,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_4__.DataTable, {\n            columns: columns,\n            data: voyages || [],\n            showToolbar: false,\n            pageSize: 20\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n            lineNumber: 219,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n        lineNumber: 215,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewVoyages, \"XpbpM7QDVNH8xd7slxHFNFoGgqc=\", false, function() {\n    return [\n        nuqs__WEBPACK_IMPORTED_MODULE_6__.useQueryState,\n        nuqs__WEBPACK_IMPORTED_MODULE_6__.useQueryState,\n        nuqs__WEBPACK_IMPORTED_MODULE_6__.useQueryState\n    ];\n});\n_c = CrewVoyages;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewVoyages);\nvar _c;\n$RefreshReg$(_c, \"CrewVoyages\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/voyages.tsx\n"));

/***/ })

});