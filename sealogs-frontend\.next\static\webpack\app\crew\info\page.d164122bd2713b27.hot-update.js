"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/voyages.tsx":
/*!*************************************!*\
  !*** ./src/app/ui/crew/voyages.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs/plugin/isBetween */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isBetween.js\");\n/* harmony import */ var dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.24_@ba_5aaac57c1bd8be9e9c8518a8e7af1c46/node_modules/nuqs/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Extend dayjs with isBetween plugin\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3___default()));\nconst CrewVoyages = (param)=>{\n    let { voyages } = param;\n    _s();\n    // State management for filters using nuqs\n    const [dateRangeFilter, setDateRangeFilter] = (0,nuqs__WEBPACK_IMPORTED_MODULE_7__.useQueryState)(\"dateRange\", {\n        defaultValue: \"\",\n        serialize: (value)=>value || \"\",\n        parse: (value)=>value || \"\"\n    });\n    const [vesselFilter, setVesselFilter] = (0,nuqs__WEBPACK_IMPORTED_MODULE_7__.useQueryState)(\"vessel\", {\n        defaultValue: \"\",\n        serialize: (value)=>value || \"\",\n        parse: (value)=>value || \"\"\n    });\n    const [dutyFilter, setDutyFilter] = (0,nuqs__WEBPACK_IMPORTED_MODULE_7__.useQueryState)(\"duty\", {\n        defaultValue: \"\",\n        serialize: (value)=>value || \"\",\n        parse: (value)=>value || \"\"\n    });\n    // Local state for filter values\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [selectedVessel, setSelectedVessel] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [selectedDuty, setSelectedDuty] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const formatDateWithTime = (dateTime)=>{\n        if (dateTime) {\n            const [date, time] = dateTime.split(\" \");\n            const [year, month, day] = date.split(\"-\");\n            return \"\".concat(day, \"/\").concat(month, \"/\").concat(year.slice(-2), \" at \").concat(time);\n        }\n    };\n    // Extract unique vessel options from voyages data\n    const vesselOptions = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(()=>{\n        if (!voyages || !Array.isArray(voyages)) return [];\n        const uniqueVessels = new Map();\n        voyages.forEach((voyage)=>{\n            var _voyage_logBookEntry;\n            const vessel = voyage === null || voyage === void 0 ? void 0 : (_voyage_logBookEntry = voyage.logBookEntry) === null || _voyage_logBookEntry === void 0 ? void 0 : _voyage_logBookEntry.vehicle;\n            if (vessel && vessel.id && vessel.title) {\n                uniqueVessels.set(vessel.id, {\n                    value: vessel.id,\n                    label: vessel.title\n                });\n            }\n        });\n        return Array.from(uniqueVessels.values()).sort((a, b)=>a.label.localeCompare(b.label));\n    }, [\n        voyages\n    ]);\n    // Extract unique duty options from voyages data\n    const dutyOptions = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(()=>{\n        if (!voyages || !Array.isArray(voyages)) return [];\n        const uniqueDuties = new Map();\n        voyages.forEach((voyage)=>{\n            const duty = voyage === null || voyage === void 0 ? void 0 : voyage.dutyPerformed;\n            if (duty && duty.id && duty.title) {\n                uniqueDuties.set(duty.id, {\n                    value: duty.id,\n                    label: duty.title\n                });\n            }\n        });\n        return Array.from(uniqueDuties.values()).sort((a, b)=>a.label.localeCompare(b.label));\n    }, [\n        voyages\n    ]);\n    // Filter voyages based on active filters\n    const filteredVoyages = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(()=>{\n        if (!voyages || !Array.isArray(voyages)) return [];\n        let filtered = [\n            ...voyages\n        ];\n        // Apply date range filter\n        if (dateRange && (dateRange.from || dateRange.to)) {\n            filtered = filtered.filter((voyage)=>{\n                const voyageDate = voyage.punchIn ? dayjs__WEBPACK_IMPORTED_MODULE_2___default()(voyage.punchIn) : dayjs__WEBPACK_IMPORTED_MODULE_2___default()(voyage.logBookEntry.startDate);\n                if (dateRange.from && dateRange.to) {\n                    return voyageDate.isBetween(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(dateRange.from).startOf(\"day\"), dayjs__WEBPACK_IMPORTED_MODULE_2___default()(dateRange.to).endOf(\"day\"), null, \"[]\");\n                } else if (dateRange.from) {\n                    return voyageDate.isAfter(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(dateRange.from).startOf(\"day\")) || voyageDate.isSame(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(dateRange.from), \"day\");\n                } else if (dateRange.to) {\n                    return voyageDate.isBefore(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(dateRange.to).endOf(\"day\")) || voyageDate.isSame(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(dateRange.to), \"day\");\n                }\n                return true;\n            });\n        }\n        // Apply vessel filter\n        if (selectedVessel) {\n            const vesselId = String(selectedVessel.value);\n            filtered = filtered.filter((voyage)=>{\n                var _voyage_logBookEntry_vehicle, _voyage_logBookEntry;\n                const voyageVesselId = String(voyage === null || voyage === void 0 ? void 0 : (_voyage_logBookEntry = voyage.logBookEntry) === null || _voyage_logBookEntry === void 0 ? void 0 : (_voyage_logBookEntry_vehicle = _voyage_logBookEntry.vehicle) === null || _voyage_logBookEntry_vehicle === void 0 ? void 0 : _voyage_logBookEntry_vehicle.id);\n                return voyageVesselId === vesselId;\n            });\n        }\n        // Apply duty performed filter\n        if (selectedDuty) {\n            const dutyId = String(selectedDuty.value);\n            filtered = filtered.filter((voyage)=>{\n                var _voyage_dutyPerformed;\n                const voyageDutyId = String(voyage === null || voyage === void 0 ? void 0 : (_voyage_dutyPerformed = voyage.dutyPerformed) === null || _voyage_dutyPerformed === void 0 ? void 0 : _voyage_dutyPerformed.id);\n                return voyageDutyId === dutyId;\n            });\n        }\n        return filtered;\n    }, [\n        voyages,\n        dateRange,\n        selectedVessel,\n        selectedDuty\n    ]);\n    // Calculate sea time in hours\n    const calculateSeaTime = (punchIn, punchOut)=>{\n        if (!punchIn || !punchOut) return \"0\";\n        const hours = Math.floor((dayjs__WEBPACK_IMPORTED_MODULE_2___default()(punchOut).valueOf() - dayjs__WEBPACK_IMPORTED_MODULE_2___default()(punchIn).valueOf()) / (1000 * 60 * 60));\n        return isNaN(hours) ? \"0\" : hours.toString();\n    };\n    // Filter handlers\n    const handleDateRangeChange = (value)=>{\n        setDateRange(value);\n        setDateRangeFilter(value ? JSON.stringify(value) : \"\");\n    };\n    const handleVesselChange = (value)=>{\n        setSelectedVessel(value);\n        setVesselFilter(value ? JSON.stringify(value) : \"\");\n    };\n    const handleDutyChange = (value)=>{\n        setSelectedDuty(value);\n        setDutyFilter(value ? JSON.stringify(value) : \"\");\n    };\n    // Initialize filters from URL on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        try {\n            if (dateRangeFilter) {\n                const parsed = JSON.parse(dateRangeFilter);\n                setDateRange(parsed);\n            }\n            if (vesselFilter) {\n                const parsed = JSON.parse(vesselFilter);\n                setSelectedVessel(parsed);\n            }\n            if (dutyFilter) {\n                const parsed = JSON.parse(dutyFilter);\n                setSelectedDuty(parsed);\n            }\n        } catch (error) {\n            console.warn(\"Error parsing filter values from URL:\", error);\n        }\n    }, [\n        dateRangeFilter,\n        vesselFilter,\n        dutyFilter\n    ]);\n    // Define columns for the DataTable\n    const columns = [\n        {\n            accessorKey: \"date\",\n            header: \"Date\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.punchIn ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(voyage.punchIn) : (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(voyage.logBookEntry.startDate);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: \"Vessel\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.logBookEntry.vehicle.title;\n            }\n        },\n        {\n            accessorKey: \"dutyPerformed\",\n            header: \"Duty performed\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.dutyPerformed.title;\n            }\n        },\n        {\n            accessorKey: \"signIn\",\n            header: \"Sign in\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return formatDateWithTime(voyage.punchIn);\n            }\n        },\n        {\n            accessorKey: \"signOut\",\n            header: \"Sign out\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return formatDateWithTime(voyage.punchOut);\n            }\n        },\n        {\n            accessorKey: \"totalSeaTime\",\n            header: \"Total sea time\",\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                const hours = calculateSeaTime(voyage.punchIn, voyage.punchOut);\n                return \"\".concat(hours, \" Hours\");\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full p-0\",\n        children: !voyages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_1__.List, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n            lineNumber: 257,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_5__.DataTable, {\n            columns: columns,\n            data: voyages || [],\n            showToolbar: false,\n            pageSize: 20\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n            lineNumber: 259,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n        lineNumber: 255,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewVoyages, \"n6Pjq8bEgHCu7+XMj1kpCWQpMIg=\", false, function() {\n    return [\n        nuqs__WEBPACK_IMPORTED_MODULE_7__.useQueryState,\n        nuqs__WEBPACK_IMPORTED_MODULE_7__.useQueryState,\n        nuqs__WEBPACK_IMPORTED_MODULE_7__.useQueryState\n    ];\n});\n_c = CrewVoyages;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewVoyages);\nvar _c;\n$RefreshReg$(_c, \"CrewVoyages\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/voyages.tsx\n"));

/***/ })

});