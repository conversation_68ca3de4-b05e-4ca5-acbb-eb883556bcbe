"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/voyages.tsx":
/*!*************************************!*\
  !*** ./src/app/ui/crew/voyages.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../components/skeletons */ \"(app-pages-browser)/./src/components/skeletons.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs/plugin/isBetween */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isBetween.js\");\n/* harmony import */ var dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.24_@babel+core@7._0c7e6f2743654e4d39a6c84bf81deb40/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Extend dayjs with isBetween plugin\ndayjs__WEBPACK_IMPORTED_MODULE_2___default().extend((dayjs_plugin_isBetween__WEBPACK_IMPORTED_MODULE_3___default()));\nconst CrewVoyages = (param)=>{\n    let { voyages } = param;\n    _s();\n    // State for filter values\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({\n        dateRange: null,\n        vessel: null,\n        duty: null\n    });\n    const formatDateWithTime = (dateTime)=>{\n        if (dateTime) {\n            const [date, time] = dateTime.split(\" \");\n            const [year, month, day] = date.split(\"-\");\n            return \"\".concat(day, \"/\").concat(month, \"/\").concat(year.slice(-2), \" at \").concat(time);\n        }\n    };\n    // Extract unique vessel options from voyages data\n    const vesselOptions = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(()=>{\n        if (!voyages || !Array.isArray(voyages)) return [];\n        const uniqueVessels = new Map();\n        voyages.forEach((voyage)=>{\n            var _voyage_logBookEntry;\n            const vessel = voyage === null || voyage === void 0 ? void 0 : (_voyage_logBookEntry = voyage.logBookEntry) === null || _voyage_logBookEntry === void 0 ? void 0 : _voyage_logBookEntry.vehicle;\n            if (vessel && vessel.id && vessel.title) {\n                uniqueVessels.set(vessel.id, {\n                    value: vessel.id,\n                    label: vessel.title\n                });\n            }\n        });\n        return Array.from(uniqueVessels.values()).sort((a, b)=>a.label.localeCompare(b.label));\n    }, [\n        voyages\n    ]);\n    // Extract unique duty options from voyages data\n    const dutyOptions = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(()=>{\n        if (!voyages || !Array.isArray(voyages)) return [];\n        const uniqueDuties = new Map();\n        voyages.forEach((voyage)=>{\n            const duty = voyage === null || voyage === void 0 ? void 0 : voyage.dutyPerformed;\n            if (duty && duty.id && duty.title) {\n                uniqueDuties.set(duty.id, {\n                    value: duty.id,\n                    label: duty.title\n                });\n            }\n        });\n        return Array.from(uniqueDuties.values()).sort((a, b)=>a.label.localeCompare(b.label));\n    }, [\n        voyages\n    ]);\n    // Filter voyages based on active filters\n    const filteredVoyages = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(()=>{\n        if (!voyages || !Array.isArray(voyages)) return [];\n        let filtered = [\n            ...voyages\n        ];\n        // Apply date range filter\n        if (filters.dateRange && (filters.dateRange.from || filters.dateRange.to)) {\n            filtered = filtered.filter((voyage)=>{\n                const voyageDate = voyage.punchIn ? dayjs__WEBPACK_IMPORTED_MODULE_2___default()(voyage.punchIn) : dayjs__WEBPACK_IMPORTED_MODULE_2___default()(voyage.logBookEntry.startDate);\n                if (filters.dateRange.from && filters.dateRange.to) {\n                    return voyageDate.isBetween(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.from).startOf(\"day\"), dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.to).endOf(\"day\"), null, \"[]\");\n                } else if (filters.dateRange.from) {\n                    return voyageDate.isAfter(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.from).startOf(\"day\")) || voyageDate.isSame(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.from), \"day\");\n                } else if (filters.dateRange.to) {\n                    return voyageDate.isBefore(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.to).endOf(\"day\")) || voyageDate.isSame(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(filters.dateRange.to), \"day\");\n                }\n                return true;\n            });\n        }\n        // Apply vessel filter\n        if (filters.vessel) {\n            const vesselId = String(filters.vessel.value);\n            filtered = filtered.filter((voyage)=>{\n                var _voyage_logBookEntry_vehicle, _voyage_logBookEntry;\n                const voyageVesselId = String(voyage === null || voyage === void 0 ? void 0 : (_voyage_logBookEntry = voyage.logBookEntry) === null || _voyage_logBookEntry === void 0 ? void 0 : (_voyage_logBookEntry_vehicle = _voyage_logBookEntry.vehicle) === null || _voyage_logBookEntry_vehicle === void 0 ? void 0 : _voyage_logBookEntry_vehicle.id);\n                return voyageVesselId === vesselId;\n            });\n        }\n        // Apply duty performed filter\n        if (filters.duty) {\n            const dutyId = String(filters.duty.value);\n            filtered = filtered.filter((voyage)=>{\n                var _voyage_dutyPerformed;\n                const voyageDutyId = String(voyage === null || voyage === void 0 ? void 0 : (_voyage_dutyPerformed = voyage.dutyPerformed) === null || _voyage_dutyPerformed === void 0 ? void 0 : _voyage_dutyPerformed.id);\n                return voyageDutyId === dutyId;\n            });\n        }\n        return filtered;\n    }, [\n        voyages,\n        filters\n    ]);\n    // Calculate sea time in hours\n    const calculateSeaTime = (punchIn, punchOut)=>{\n        if (!punchIn || !punchOut) return \"0\";\n        const hours = Math.floor((dayjs__WEBPACK_IMPORTED_MODULE_2___default()(punchOut).valueOf() - dayjs__WEBPACK_IMPORTED_MODULE_2___default()(punchIn).valueOf()) / (1000 * 60 * 60));\n        return isNaN(hours) ? \"0\" : hours.toString();\n    };\n    // Handle filter changes from toolbar\n    const handleFilterChange = (param)=>{\n        let { type, data } = param;\n        setFilters((prev)=>({\n                ...prev,\n                [type]: data\n            }));\n    };\n    // Define columns for the DataTable\n    const columns = [\n        {\n            accessorKey: \"date\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.punchIn ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(voyage.punchIn) : (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_4__.formatDate)(voyage.logBookEntry.startDate);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original_logBookEntry, _rowA_original1, _rowB_original, _rowB_original_logBookEntry, _rowB_original1;\n                const dateA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.punchIn) ? new Date(rowA.original.punchIn).getTime() : new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_logBookEntry = _rowA_original1.logBookEntry) === null || _rowA_original_logBookEntry === void 0 ? void 0 : _rowA_original_logBookEntry.startDate) || 0).getTime();\n                const dateB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.punchIn) ? new Date(rowB.original.punchIn).getTime() : new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_logBookEntry = _rowB_original1.logBookEntry) === null || _rowB_original_logBookEntry === void 0 ? void 0 : _rowB_original_logBookEntry.startDate) || 0).getTime();\n                return dateB - dateA // Most recent first\n                ;\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.logBookEntry.vehicle.title;\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_logBookEntry_vehicle, _rowA_original_logBookEntry, _rowA_original, _rowB_original_logBookEntry_vehicle, _rowB_original_logBookEntry, _rowB_original;\n                const vesselA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_logBookEntry = _rowA_original.logBookEntry) === null || _rowA_original_logBookEntry === void 0 ? void 0 : (_rowA_original_logBookEntry_vehicle = _rowA_original_logBookEntry.vehicle) === null || _rowA_original_logBookEntry_vehicle === void 0 ? void 0 : _rowA_original_logBookEntry_vehicle.title) || \"\";\n                const vesselB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_logBookEntry = _rowB_original.logBookEntry) === null || _rowB_original_logBookEntry === void 0 ? void 0 : (_rowB_original_logBookEntry_vehicle = _rowB_original_logBookEntry.vehicle) === null || _rowB_original_logBookEntry_vehicle === void 0 ? void 0 : _rowB_original_logBookEntry_vehicle.title) || \"\";\n                return vesselA.localeCompare(vesselB);\n            }\n        },\n        {\n            accessorKey: \"dutyPerformed\",\n            header: \"Duty performed\",\n            cellAlignment: \"center\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return voyage.dutyPerformed.title;\n            }\n        },\n        {\n            accessorKey: \"signIn\",\n            header: \"Sign in\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return formatDateWithTime(voyage.punchIn);\n            }\n        },\n        {\n            accessorKey: \"signOut\",\n            header: \"Sign out\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                return formatDateWithTime(voyage.punchOut);\n            }\n        },\n        {\n            accessorKey: \"totalSeaTime\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_7__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Total sea time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                const voyage = row.original;\n                const hours = calculateSeaTime(voyage.punchIn, voyage.punchOut);\n                return \"\".concat(hours, \" Hours\");\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                const hoursA = parseInt(calculateSeaTime(rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.punchIn, rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.punchOut)) || 0;\n                const hoursB = parseInt(calculateSeaTime(rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.punchIn, rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.punchOut)) || 0;\n                return hoursB - hoursA // Highest hours first\n                ;\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full p-0\",\n        children: !voyages ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_1__.List, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n            lineNumber: 260,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_5__.DataTable, {\n            columns: columns,\n            data: filteredVoyages,\n            showToolbar: true,\n            pageSize: 20,\n            onChange: handleFilterChange\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n            lineNumber: 262,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\voyages.tsx\",\n        lineNumber: 258,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewVoyages, \"SijZc/iWy8z8KncShVoxjuPcP3A=\");\n_c = CrewVoyages;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewVoyages);\nvar _c;\n$RefreshReg$(_c, \"CrewVoyages\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/voyages.tsx\n"));

/***/ })

});