'use client'
import { List } from '../../../components/skeletons'
import dayjs from 'dayjs'
import { formatDate } from '@/app/helpers/dateHelper'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable'
import { useState, useEffect } from 'react'

const CrewVoyages = ({ voyages }: { voyages?: any }) => {
    const [filters, setFilters] = useState<any>({})
    const [filteredVoyages, setFilteredVoyages] = useState<any[]>([])

    // Handle filter changes
    const handleFilterOnChange = ({ type, data }: { type: string; data: any }) => {
        setFilters((prev: any) => ({
            ...prev,
            [type]: data,
        }))
    }

    // Apply filters whenever filters or voyages change
    useEffect(() => {
        if (!voyages) return

        let filtered = [...voyages]

        // Apply date range filter
        if (filters.dateRange && (filters.dateRange.startDate || filters.dateRange.endDate)) {
            const startDate = filters.dateRange.startDate ? dayjs(filters.dateRange.startDate).startOf('day') : null
            const endDate = filters.dateRange.endDate ? dayjs(filters.dateRange.endDate).endOf('day') : null

            filtered = filtered.filter((voyage: any) => {
                const voyageDate = voyage.punchIn
                    ? dayjs(voyage.punchIn)
                    : dayjs(voyage.logBookEntry.startDate)

                if (startDate && endDate) {
                    return voyageDate.isBetween(startDate, endDate, null, '[]')
                } else if (startDate) {
                    return voyageDate.isAfter(startDate) || voyageDate.isSame(startDate, 'day')
                } else if (endDate) {
                    return voyageDate.isBefore(endDate) || voyageDate.isSame(endDate, 'day')
                }
                return true
            })
        }

        // Apply vessel filter
        if (filters.vessel) {
            let vesselIds: string[] = []
            if (Array.isArray(filters.vessel) && filters.vessel.length > 0) {
                vesselIds = filters.vessel.map((item: any) => String(item.value))
            } else if (filters.vessel && !Array.isArray(filters.vessel)) {
                vesselIds = [String(filters.vessel.value)]
            }

            if (vesselIds.length > 0) {
                filtered = filtered.filter((voyage: any) => {
                    const vesselId = String(voyage?.logBookEntry?.vehicle?.id)
                    return vesselIds.includes(vesselId)
                })
            }
        }

        // Apply duty performed filter
        if (filters.dutyPerformed) {
            let dutyIds: string[] = []
            if (Array.isArray(filters.dutyPerformed) && filters.dutyPerformed.length > 0) {
                dutyIds = filters.dutyPerformed.map((item: any) => String(item.value))
            } else if (filters.dutyPerformed && !Array.isArray(filters.dutyPerformed)) {
                dutyIds = [String(filters.dutyPerformed.value)]
            }

            if (dutyIds.length > 0) {
                filtered = filtered.filter((voyage: any) => {
                    const dutyId = String(voyage?.dutyPerformed?.id)
                    return dutyIds.includes(dutyId)
                })
            }
        }

        setFilteredVoyages(filtered)
    }, [filters, voyages])
    const formatDateWithTime = (dateTime: any) => {
        if (dateTime) {
            const [date, time] = dateTime.split(' ')
            const [year, month, day] = date.split('-')
            return `${day}/${month}/${year.slice(-2)} at ${time}`
        }
    }

    // Calculate sea time in hours
    const calculateSeaTime = (punchIn: any, punchOut: any) => {
        if (!punchIn || !punchOut) return '0'

        const hours = Math.floor(
            (dayjs(punchOut).valueOf() - dayjs(punchIn).valueOf()) /
                (1000 * 60 * 60),
        )

        return isNaN(hours) ? '0' : hours.toString()
    }

    // Define columns for the DataTable
    const columns: ExtendedColumnDef<any, any>[] = [
        {
            accessorKey: 'date',
            header: 'Date',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return voyage.punchIn
                    ? formatDate(voyage.punchIn)
                    : formatDate(voyage.logBookEntry.startDate)
            },
        },
        {
            accessorKey: 'vessel',
            header: 'Vessel',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return voyage.logBookEntry.vehicle.title
            },
        },
        {
            accessorKey: 'dutyPerformed',
            header: 'Duty performed',
            cellAlignment: 'center',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return voyage.dutyPerformed.title
            },
        },
        {
            accessorKey: 'signIn',
            header: 'Sign in',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return formatDateWithTime(voyage.punchIn)
            },
        },
        {
            accessorKey: 'signOut',
            header: 'Sign out',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return formatDateWithTime(voyage.punchOut)
            },
        },
        {
            accessorKey: 'totalSeaTime',
            header: 'Total sea time',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                const hours = calculateSeaTime(voyage.punchIn, voyage.punchOut)
                return `${hours} Hours`
            },
        },
    ]

    return (
        <div className="w-full p-0">
            {!voyages ? (
                <List />
            ) : (
                <DataTable
                    columns={columns}
                    data={voyages || []}
                    showToolbar={false}
                    pageSize={20}
                />
            )}
        </div>
    )
}

export default CrewVoyages
