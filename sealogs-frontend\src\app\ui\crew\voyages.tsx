'use client'
import { List } from '../../../components/skeletons'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import { formatDate } from '@/app/helpers/dateHelper'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable'
import { useState, useMemo } from 'react'
import { DataTableSortHeader } from '@/components/data-table-sort-header'

// Extend dayjs with isBetween plugin
dayjs.extend(isBetween)

const CrewVoyages = ({ voyages }: { voyages?: any }) => {
    // State for filter values
    const [filters, setFilters] = useState<any>({
        dateRange: null,
        vessel: null,
        duty: null,
    })

    const formatDateWithTime = (dateTime: any) => {
        if (dateTime) {
            const [date, time] = dateTime.split(' ')
            const [year, month, day] = date.split('-')
            return `${day}/${month}/${year.slice(-2)} at ${time}`
        }
    }

    // Filter voyages based on active filters
    const filteredVoyages = useMemo(() => {
        if (!voyages || !Array.isArray(voyages)) return []

        let filtered = [...voyages]

        // Apply date range filter
        if (
            filters.dateRange &&
            (filters.dateRange.startDate || filters.dateRange.endDate)
        ) {
            filtered = filtered.filter((voyage: any) => {
                const voyageDate = voyage.punchIn
                    ? dayjs(voyage.punchIn)
                    : dayjs(voyage.logBookEntry.startDate)

                if (filters.dateRange.startDate && filters.dateRange.endDate) {
                    return voyageDate.isBetween(
                        dayjs(filters.dateRange.startDate).startOf('day'),
                        dayjs(filters.dateRange.endDate).endOf('day'),
                        null,
                        '[]',
                    )
                } else if (filters.dateRange.startDate) {
                    return (
                        voyageDate.isAfter(
                            dayjs(filters.dateRange.startDate).startOf('day'),
                        ) ||
                        voyageDate.isSame(
                            dayjs(filters.dateRange.startDate),
                            'day',
                        )
                    )
                } else if (filters.dateRange.endDate) {
                    return (
                        voyageDate.isBefore(
                            dayjs(filters.dateRange.endDate).endOf('day'),
                        ) ||
                        voyageDate.isSame(
                            dayjs(filters.dateRange.endDate),
                            'day',
                        )
                    )
                }
                return true
            })
        }

        // Apply vessel filter
        if (filters.vessel) {
            const vesselId = String(filters.vessel.value)
            filtered = filtered.filter((voyage: any) => {
                const voyageVesselId = String(voyage?.logBookEntry?.vehicle?.id)
                return voyageVesselId === vesselId
            })
        }

        // Apply duty performed filter
        if (filters.duty) {
            const dutyId = String(filters.duty.value)
            filtered = filtered.filter((voyage: any) => {
                const voyageDutyId = String(voyage?.dutyPerformed?.id)
                return voyageDutyId === dutyId
            })
        }

        return filtered
    }, [voyages, filters])

    // Calculate sea time in hours
    const calculateSeaTime = (punchIn: any, punchOut: any) => {
        if (!punchIn || !punchOut) return '0'

        const hours = Math.floor(
            (dayjs(punchOut).valueOf() - dayjs(punchIn).valueOf()) /
                (1000 * 60 * 60),
        )

        return isNaN(hours) ? '0' : hours.toString()
    }

    // Handle filter changes from toolbar
    const handleFilterChange = ({ type, data }: any) => {
        setFilters((prev: any) => ({
            ...prev,
            [type]: data,
        }))
    }

    // Define columns for the DataTable
    const columns: ExtendedColumnDef<any, any>[] = [
        {
            accessorKey: 'date',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Date" />
            ),
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return voyage.punchIn
                    ? formatDate(voyage.punchIn)
                    : formatDate(voyage.logBookEntry.startDate)
            },
            sortingFn: (rowA: any, rowB: any) => {
                const dateA = rowA?.original?.punchIn
                    ? new Date(rowA.original.punchIn).getTime()
                    : new Date(
                          rowA?.original?.logBookEntry?.startDate || 0,
                      ).getTime()
                const dateB = rowB?.original?.punchIn
                    ? new Date(rowB.original.punchIn).getTime()
                    : new Date(
                          rowB?.original?.logBookEntry?.startDate || 0,
                      ).getTime()

                return dateB - dateA // Most recent first
            },
        },
        {
            accessorKey: 'vessel',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Vessel" />
            ),
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return voyage.logBookEntry.vehicle.title
            },
            sortingFn: (rowA: any, rowB: any) => {
                const vesselA =
                    rowA?.original?.logBookEntry?.vehicle?.title || ''
                const vesselB =
                    rowB?.original?.logBookEntry?.vehicle?.title || ''
                return vesselA.localeCompare(vesselB)
            },
        },
        {
            accessorKey: 'dutyPerformed',
            header: 'Duty performed',
            cellAlignment: 'center',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return voyage.dutyPerformed.title
            },
        },
        {
            accessorKey: 'signIn',
            header: 'Sign in',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return formatDateWithTime(voyage.punchIn)
            },
        },
        {
            accessorKey: 'signOut',
            header: 'Sign out',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return formatDateWithTime(voyage.punchOut)
            },
        },
        {
            accessorKey: 'totalSeaTime',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Total sea time" />
            ),
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                const hours = calculateSeaTime(voyage.punchIn, voyage.punchOut)
                return `${hours} Hours`
            },
            sortingFn: (rowA: any, rowB: any) => {
                const hoursA =
                    parseInt(
                        calculateSeaTime(
                            rowA?.original?.punchIn,
                            rowA?.original?.punchOut,
                        ),
                    ) || 0
                const hoursB =
                    parseInt(
                        calculateSeaTime(
                            rowB?.original?.punchIn,
                            rowB?.original?.punchOut,
                        ),
                    ) || 0
                return hoursB - hoursA // Highest hours first
            },
        },
    ]

    return (
        <div className="w-full p-0">
            {!voyages ? (
                <List />
            ) : (
                <DataTable
                    columns={columns}
                    data={filteredVoyages}
                    showToolbar={true}
                    pageSize={20}
                    onChange={handleFilterChange}
                />
            )}
        </div>
    )
}

export default CrewVoyages
