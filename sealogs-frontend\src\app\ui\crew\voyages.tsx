'use client'
import { List } from '../../../components/skeletons'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import { formatDate } from '@/app/helpers/dateHelper'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable'
import { useState, useEffect, useMemo } from 'react'
import { useQueryState } from 'nuqs'
import DatePicker from '@/components/DateRange'
import { Combobox } from '@/components/ui/comboBox'
import { DataTableSortHeader } from '@/components/data-table-sort-header'

// Extend dayjs with isBetween plugin
dayjs.extend(isBetween)

const CrewVoyages = ({ voyages }: { voyages?: any }) => {
    // State management for filters using nuqs
    const [dateRangeFilter, setDateRangeFilter] = useQueryState('dateRange', {
        defaultValue: '',
        serialize: (value) => value || '',
        parse: (value) => value || '',
    })
    const [vesselFilter, setVesselFilter] = useQueryState('vessel', {
        defaultValue: '',
        serialize: (value) => value || '',
        parse: (value) => value || '',
    })
    const [dutyFilter, setDutyFilter] = useQueryState('duty', {
        defaultValue: '',
        serialize: (value) => value || '',
        parse: (value) => value || '',
    })

    // Local state for filter values
    const [dateRange, setDateRange] = useState<any>(null)
    const [selectedVessel, setSelectedVessel] = useState<any>(null)
    const [selectedDuty, setSelectedDuty] = useState<any>(null)

    const formatDateWithTime = (dateTime: any) => {
        if (dateTime) {
            const [date, time] = dateTime.split(' ')
            const [year, month, day] = date.split('-')
            return `${day}/${month}/${year.slice(-2)} at ${time}`
        }
    }

    // Extract unique vessel options from voyages data
    const vesselOptions = useMemo(() => {
        if (!voyages || !Array.isArray(voyages)) return []

        const uniqueVessels = new Map()
        voyages.forEach((voyage: any) => {
            const vessel = voyage?.logBookEntry?.vehicle
            if (vessel && vessel.id && vessel.title) {
                uniqueVessels.set(vessel.id, {
                    value: vessel.id,
                    label: vessel.title,
                })
            }
        })

        return Array.from(uniqueVessels.values()).sort((a: any, b: any) =>
            a.label.localeCompare(b.label),
        )
    }, [voyages])

    // Extract unique duty options from voyages data
    const dutyOptions = useMemo(() => {
        if (!voyages || !Array.isArray(voyages)) return []

        const uniqueDuties = new Map()
        voyages.forEach((voyage: any) => {
            const duty = voyage?.dutyPerformed
            if (duty && duty.id && duty.title) {
                uniqueDuties.set(duty.id, {
                    value: duty.id,
                    label: duty.title,
                })
            }
        })

        return Array.from(uniqueDuties.values()).sort((a: any, b: any) =>
            a.label.localeCompare(b.label),
        )
    }, [voyages])

    // Filter voyages based on active filters
    const filteredVoyages = useMemo(() => {
        if (!voyages || !Array.isArray(voyages)) return []

        let filtered = [...voyages]

        // Apply date range filter
        if (dateRange && (dateRange.from || dateRange.to)) {
            filtered = filtered.filter((voyage: any) => {
                const voyageDate = voyage.punchIn
                    ? dayjs(voyage.punchIn)
                    : dayjs(voyage.logBookEntry.startDate)

                if (dateRange.from && dateRange.to) {
                    return voyageDate.isBetween(
                        dayjs(dateRange.from).startOf('day'),
                        dayjs(dateRange.to).endOf('day'),
                        null,
                        '[]',
                    )
                } else if (dateRange.from) {
                    return (
                        voyageDate.isAfter(
                            dayjs(dateRange.from).startOf('day'),
                        ) || voyageDate.isSame(dayjs(dateRange.from), 'day')
                    )
                } else if (dateRange.to) {
                    return (
                        voyageDate.isBefore(dayjs(dateRange.to).endOf('day')) ||
                        voyageDate.isSame(dayjs(dateRange.to), 'day')
                    )
                }
                return true
            })
        }

        // Apply vessel filter
        if (selectedVessel) {
            const vesselId = String(selectedVessel.value)
            filtered = filtered.filter((voyage: any) => {
                const voyageVesselId = String(voyage?.logBookEntry?.vehicle?.id)
                return voyageVesselId === vesselId
            })
        }

        // Apply duty performed filter
        if (selectedDuty) {
            const dutyId = String(selectedDuty.value)
            filtered = filtered.filter((voyage: any) => {
                const voyageDutyId = String(voyage?.dutyPerformed?.id)
                return voyageDutyId === dutyId
            })
        }

        return filtered
    }, [voyages, dateRange, selectedVessel, selectedDuty])

    // Calculate sea time in hours
    const calculateSeaTime = (punchIn: any, punchOut: any) => {
        if (!punchIn || !punchOut) return '0'

        const hours = Math.floor(
            (dayjs(punchOut).valueOf() - dayjs(punchIn).valueOf()) /
                (1000 * 60 * 60),
        )

        return isNaN(hours) ? '0' : hours.toString()
    }

    // Filter handlers
    const handleDateRangeChange = (value: any) => {
        setDateRange(value)
        setDateRangeFilter(value ? JSON.stringify(value) : '')
    }

    const handleVesselChange = (value: any) => {
        setSelectedVessel(value)
        setVesselFilter(value ? JSON.stringify(value) : '')
    }

    const handleDutyChange = (value: any) => {
        setSelectedDuty(value)
        setDutyFilter(value ? JSON.stringify(value) : '')
    }

    // Initialize filters from URL on component mount
    useEffect(() => {
        try {
            if (dateRangeFilter) {
                const parsed = JSON.parse(dateRangeFilter)
                setDateRange(parsed)
            }
            if (vesselFilter) {
                const parsed = JSON.parse(vesselFilter)
                setSelectedVessel(parsed)
            }
            if (dutyFilter) {
                const parsed = JSON.parse(dutyFilter)
                setSelectedDuty(parsed)
            }
        } catch (error) {
            console.warn('Error parsing filter values from URL:', error)
        }
    }, [dateRangeFilter, vesselFilter, dutyFilter])

    // Define columns for the DataTable
    const columns: ExtendedColumnDef<any, any>[] = [
        {
            accessorKey: 'date',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Date" />
            ),
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return voyage.punchIn
                    ? formatDate(voyage.punchIn)
                    : formatDate(voyage.logBookEntry.startDate)
            },
            sortingFn: (rowA: any, rowB: any) => {
                const dateA = rowA?.original?.punchIn
                    ? new Date(rowA.original.punchIn).getTime()
                    : new Date(
                          rowA?.original?.logBookEntry?.startDate || 0,
                      ).getTime()
                const dateB = rowB?.original?.punchIn
                    ? new Date(rowB.original.punchIn).getTime()
                    : new Date(
                          rowB?.original?.logBookEntry?.startDate || 0,
                      ).getTime()

                return dateB - dateA // Most recent first
            },
        },
        {
            accessorKey: 'vessel',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Vessel" />
            ),
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return voyage.logBookEntry.vehicle.title
            },
            sortingFn: (rowA: any, rowB: any) => {
                const vesselA =
                    rowA?.original?.logBookEntry?.vehicle?.title || ''
                const vesselB =
                    rowB?.original?.logBookEntry?.vehicle?.title || ''
                return vesselA.localeCompare(vesselB)
            },
        },
        {
            accessorKey: 'dutyPerformed',
            header: 'Duty performed',
            cellAlignment: 'center',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return voyage.dutyPerformed.title
            },
        },
        {
            accessorKey: 'signIn',
            header: 'Sign in',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return formatDateWithTime(voyage.punchIn)
            },
        },
        {
            accessorKey: 'signOut',
            header: 'Sign out',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return formatDateWithTime(voyage.punchOut)
            },
        },
        {
            accessorKey: 'totalSeaTime',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Total sea time" />
            ),
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                const hours = calculateSeaTime(voyage.punchIn, voyage.punchOut)
                return `${hours} Hours`
            },
            sortingFn: (rowA: any, rowB: any) => {
                const hoursA =
                    parseInt(
                        calculateSeaTime(
                            rowA?.original?.punchIn,
                            rowA?.original?.punchOut,
                        ),
                    ) || 0
                const hoursB =
                    parseInt(
                        calculateSeaTime(
                            rowB?.original?.punchIn,
                            rowB?.original?.punchOut,
                        ),
                    ) || 0
                return hoursB - hoursA // Highest hours first
            },
        },
    ]

    return (
        <div className="w-full p-0">
            {!voyages ? (
                <List />
            ) : (
                <>
                    {/* Filter Section */}
                    <div className="mb-4 space-y-4">
                        <div className="flex flex-col md:flex-row gap-4">
                            {/* Date Range Filter */}
                            <div className="flex-1 min-w-0">
                                <DatePicker
                                    mode="range"
                                    type="date"
                                    placeholder="Select date range"
                                    value={dateRange}
                                    onChange={handleDateRangeChange}
                                    clearable
                                    className="w-full"
                                />
                            </div>

                            {/* Vessel Filter */}
                            <div className="flex-1 min-w-0">
                                <Combobox
                                    options={vesselOptions}
                                    value={selectedVessel}
                                    onChange={handleVesselChange}
                                    placeholder="Select vessel"
                                    title="Vessel"
                                    multi={false}
                                />
                            </div>

                            {/* Duty Performed Filter */}
                            <div className="flex-1 min-w-0">
                                <Combobox
                                    options={dutyOptions}
                                    value={selectedDuty}
                                    onChange={handleDutyChange}
                                    placeholder="Select duty"
                                    title="Duty"
                                    multi={false}
                                />
                            </div>
                        </div>

                        {/* Active Filters Indicator */}
                        {(dateRange || selectedVessel || selectedDuty) && (
                            <div className="text-sm text-muted-foreground">
                                Showing {filteredVoyages.length} of{' '}
                                {voyages.length} voyages
                                {dateRange && ' • Date filtered'}
                                {selectedVessel &&
                                    ` • Vessel: ${selectedVessel.label}`}
                                {selectedDuty &&
                                    ` • Duty: ${selectedDuty.label}`}
                            </div>
                        )}
                    </div>

                    {/* Data Table */}
                    <DataTable
                        columns={columns}
                        data={filteredVoyages}
                        showToolbar={false}
                        pageSize={20}
                    />
                </>
            )}
        </div>
    )
}

export default CrewVoyages
